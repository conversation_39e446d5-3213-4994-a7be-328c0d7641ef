@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --tile: rgba(59, 130, 246, 0.15);
  --border: #e5e5e5;
  --muted: #f5f5f5;
}


@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-border: var(--border);
  --color-muted: var(--muted);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --tile: rgba(59, 130, 246, 0.08);
    --border: #262626;
    --muted: #171717;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* MenuBar Component Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.tooltip-content {
  display: inline-block;
  white-space: nowrap;
}

.tooltip-content-wrapper {
  position: relative;
  height: 16px;
  overflow: hidden;
  animation: fadeIn 150ms ease-out;
}

.tooltip-label {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  opacity: 1;
  transform: translateY(0);
  transition: all 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

[data-state='closed'] {
  opacity: 0;
  transition: opacity 150ms ease-out;
}

[data-state='open'] {
  opacity: 1;
  transition: opacity 150ms ease-out, width 800ms cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(-100%);
    opacity: 0;
  }
}

.tooltip-content.slide-in {
  animation: slideIn 150ms ease-out;
}

.tooltip-content.slide-out {
  animation: slideOut 150ms ease-out;
}

@keyframes slideInY {
  from {
    transform: translateY(-5px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.tooltip-animation {
  animation: fadeIn 0.3s ease-in-out, slideInY 0.3s ease-in-out;
}
