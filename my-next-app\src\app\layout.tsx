import type { Metadata } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import { MenuBarDemo } from "./Components/ui/menu-demo";
import { Container } from "./Components/ui/container";

const inter = Inter({weight:["500","600","700","800","900"]})



export const metadata: Metadata = {
  title: "Minimal Portfolio Website",
  description: "I'm a software engineer ",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body
        className={`${inter.className} $ antialiased bg-neutral-100 dark:bg-neutral-700 `}
      >
        <Container>
          <div className="flex justify-center py-16">
            <MenuBarDemo  className = " py-6"/>
          </div>
        </Container>
        {children}
      </body>
    </html>
  );
}
