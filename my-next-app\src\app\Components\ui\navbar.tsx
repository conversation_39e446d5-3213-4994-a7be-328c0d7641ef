"use client";
import React, { useState } from 'react'
import { Container } from "./container";
import Link from "next/link"
import { motion } from "framer-motion";
import Image from "next/image";



const Navbar = () => {
    const navItems = [
        {title : "About ",
            href : '/about',
        },
        {title : 'Project',
            href : '/project',
        },
        {title : 'Contact',
            href : '/contact',
        },
        {title : 'Blog',
            href : '/blog',
        },
    ];
    const [hovered, setHovered] = useState<number | null>(null);
  return (
    <Container >
        <nav className=' flex items-center justify-between p-2   border '>
            <Image
            className='h-10 w-10 rounded-full border-2 border-neutral-200 shadow-sm'
            src="/Image/Uzui.png"
            width={40}
            height={40}
            alt='Uzui Avatar' 
            />
            <div className='flex items-center'>
                {navItems.map((item,idx) => {
                    return (
                        <Link className="text-sm relative px-2 py-1 " href={item.href} key={idx}>
                           
                           {hovered == idx &&  (
                               <motion.span layoutId = "" className ="absolute inset-0 h-full w-full rounded-md bg-neutral-100 "></motion.span>
                             
                           )} 
                           {item.title}
                            
                        </Link>

                    );
                })}
            </div>

        </nav>
    </Container>
  )
}

export default Navbar