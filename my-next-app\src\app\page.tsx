import { Tiles } from "@/app/Components/tiles";
import {Container }from "./Components/ui/container";

export default function Home() {
  return (
    <main className="relative min-h-[70vh] md:min-h-[80vh] lg:min-h-screen overflow-hidden">
      <div className="pointer-events-none absolute inset-0 -z-10">
        <Tiles rows={60} cols={10} tileSize="md" />
      </div>
      <Container className="relative z-10 mx-auto max-w-4xl px-6 py-12 md:py-16 min-h-screen">
        
      <section className="relative z-10 mx-auto max-w-4xl px-6 py-24 md:py-32">
        <h1 className="text-3xl md:text-5xl font-bold tracking-tight">Minimal Portfolio</h1>
        <p className="mt-4 text-base md:text-lg text-neutral-600 dark:text-neutral-300 text-secondary">
          A Next.js + Tailwind + TypeScript setup with a tiled hover background.
        </p>
      </section> 
      </Container>
    
    
   
    </main>      
  );
}

